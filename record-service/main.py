import logging
import logging.config
import asyncio
import os
import uuid
import threading
from aiohttp import ClientTimeout
from livekit import api, rtc
import json
import pika
from google.cloud import storage
from datetime import datetime

logging.config.fileConfig("log_conf.ini")
LOGGER = logging.getLogger(__name__)

# Use a class to manage state instead of global variables
class ApplicationState:
    def __init__(self):
        self.tasks = {}
        self.background_tasks = {}
        self.is_recording = False
        self.egress_info = {}

# Global application state
app_state = ApplicationState()

# Initialize Google Cloud Storage client
storage_client = storage.Client()
bucket_name = os.getenv('GCS_BUCKET_NAME')


exchange_name = os.getenv('RABBITMQ_EXCHANGE_NAME')

def create_rabbitmq_channel_connection():
    """Create a new RabbitMQ connection with proper timeout settings"""
    try:
        credentials = pika.PlainCredentials(os.getenv('RABBITMQ_USER'), os.getenv('RABBITMQ_PASSWORD'))
        connection = pika.BlockingConnection(
            pika.ConnectionParameters(
                host=os.getenv('RABBITMQ_HOST'),
                port=os.getenv('RABBITMQ_PORT'),
                credentials=credentials)
            )
        channel = connection.channel()
        channel.basic_qos(prefetch_count=1)
        channel.exchange_declare(exchange=exchange_name, exchange_type=os.getenv('RABBITMQ_EXCHANGE_TYPE'), passive=True)
        return connection, channel
    except Exception as e:
        LOGGER.error(f"Failed to create RabbitMQ connection: {str(e)}", exc_info=True)
        raise

def close_connection(connection, channel):
    try:
        if channel and not channel.is_closed:
            channel.close()
        if connection and not connection.is_closed:
            connection.close()
    except Exception as e:
        LOGGER.warning(f"Error closing connection: {str(e)}")

main_connection, main_channel = create_rabbitmq_channel_connection()
queue_name = os.getenv('RABBITMQ_QUEUE_NAME')
queue = main_channel.queue_declare(queue=queue_name, passive=True)

# Get the message count
message_count = queue.method.message_count

# Check if the queue is empty
if message_count == 0:
    LOGGER.info("Don't have any message in queue. Stop the server.")
    close_connection(main_connection, main_channel)
    os._exit(0)

class LiveKitConfig:
    url = os.getenv('LIVEKIT_SERVER_URL')
    apiKey = os.getenv('LIVEKIT_API_KEY')
    apiSecret = os.getenv('LIVEKIT_API_SECRET')


def stop_server(queue_channel, queue_method):
    LOGGER.info("Stop streaming EXIT server")
    try:
        # Delete received message from queue
        queue_channel.basic_ack(delivery_tag=queue_method.delivery_tag)
        os._exit(0)
    except Exception as e:
        LOGGER.error(f"Error in stop_server: {str(e)}", exc_info=True)
        os._exit(1)
    finally:
        close_connection(main_connection, main_channel)
        LOGGER.info("Stop heart_beat_thread due to metadata")

current_date = datetime.now().strftime("%Y-%m-%d")
gcs_folder = f"recordings/{current_date}"

async def start_recording_egress(track_sid: str, room_name: str):
    try:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        request = api.TrackEgressRequest(
            room_name=room_name,
            track_id=track_sid,
            file=api.DirectFileOutput(
                filepath=f"{gcs_folder}/origin/{track_sid}_{timestamp}.mp4",
                gcp=api.GCPUpload(
                    bucket=bucket_name
                ),
            ),
        )
        lkapi = api.LiveKitAPI(url=LiveKitConfig.url, api_key=LiveKitConfig.apiKey, api_secret=LiveKitConfig.apiSecret,timeout=ClientTimeout(60))
        egress_info = await lkapi.egress.start_track_egress(request)
        app_state.egress_info[track_sid] = egress_info.egress_id
    except Exception as e:
        LOGGER.error(f"Error start recording egress: {str(e)}", exc_info=True)
        raise

async def handle_start_recording(room_name: str):
    LOGGER.info(f"Starting recording")
    try:
        for track_sid in app_state.tasks.keys():
            await start_recording_egress(track_sid, room_name)
    except Exception as e:
        LOGGER.error(f"Error in track creation/publishing: {str(e)}", exc_info=True)
        raise
    app_state.is_recording = True
    return {"status": "Recording started"}

def publish_message_to_queue(message: str):
    """Synchronous function to publish message to RabbitMQ queue"""
    # Create a separate connection for publishing to avoid thread safety issues
    try:
        LOGGER.info(f"Publishing message to queue: {message}")

        # Create dedicated connection for this publish operation
        publish_connection, publish_channel = create_rabbitmq_channel_connection()

        # Publish the message
        publish_channel.basic_publish(
            exchange=exchange_name,
            routing_key=os.getenv('RABBITMQ_PUBLISH_QUEUE_NAME'),
            body=message.encode('utf-8')
        )
        LOGGER.info(f"Successfully published message: {message}")

    except Exception as e:
        LOGGER.error(f"Error publishing message to queue: {str(e)}", exc_info=True)
        raise
    finally:
        # Always close the dedicated connection
        try:
            close_connection(publish_connection, publish_channel)
        except Exception as e:
            LOGGER.warning(f"Error closing publish connection: {str(e)}")


async def handle_stop_recording(room: rtc.Room):
    LOGGER.info(f"Stopping recording from")
    try:
        # Process each track independently to avoid race conditions
        for track_sid in app_state.tasks.keys():
            request = api.StopEgressRequest(egress_id=app_state.egress_info[track_sid])
            lkapi = api.LiveKitAPI(url=LiveKitConfig.url, api_key=LiveKitConfig.apiKey, api_secret=LiveKitConfig.apiSecret,timeout=ClientTimeout(60))
            _ = await lkapi.egress.stop_egress(request)
        app_state.egress_info.clear()
        # Publish the gcs_folder string to the publish_queue immediately
        # Use run_in_executor to run the synchronous publish in a thread pool
        loop = asyncio.get_event_loop()
        await loop.run_in_executor(None, publish_message_to_queue, gcs_folder)
    except Exception as e:
        LOGGER.error(f"Error in handle_stop_recording: {str(e)}", exc_info=True)
        app_state.is_recording = False
        raise

    await room.local_participant.publish_data(bytes("DONE_UPLOADED", 'utf-8'))
    return {"status": "Recording stopped"}


async def main(room: rtc.Room, message, queue_channel, queue_method) -> None:
    @room.on("participant_disconnected")
    def on_participant_disconnected(participant: rtc.RemoteParticipant):
        LOGGER.info(f"PARTICIPANT DISCONECT")
        LOGGER.info(f"nbParticipant = {len(room.remote_participants)}")
        if len(room.remote_participants) == 0:
            stop_server(queue_channel, queue_method)

    @room.on("track_subscribed")
    def on_track_subscribed(track: rtc.Track):
        if track.kind == rtc.TrackKind.KIND_VIDEO:
            track_sid = track._info.sid
            app_state.tasks[track_sid] = True
            if app_state.is_recording and track_sid not in app_state.egress_info:
                task = asyncio.create_task(start_recording_egress(track_sid, room.name))
                app_state.background_tasks.add(task)
                task.add_done_callback(app_state.background_tasks.discard)

    @room.on("track_unsubscribed")
    def on_track_unsubscribed(
        track: rtc.Track,
        publication: rtc.RemoteTrackPublication,
        participant: rtc.RemoteParticipant,
    ):
        if track.kind == rtc.TrackKind.KIND_VIDEO:
            track_sid = track._info.sid
            LOGGER.info(f"Track unsubscribed: {track_sid}")
            # Cancel the processing task
            if track_sid in app_state.tasks:
                try:
                    app_state.tasks.pop(track_sid, None)
                except Exception as e:
                    LOGGER.error(f"Error cancelling task for {track_sid}: {e}")

    @room.on("data_received")
    def on_data_received(data: rtc.DataPacket):
        LOGGER.info(f"received data from {data.participant._info.sid}: {data.data}")
        if data.data.decode("utf-8") == "START_RECORDING":
            app_state.is_recording = True
            # FIXED: Properly track background tasks
            task = asyncio.create_task(handle_start_recording(room.name))
            app_state.background_tasks.add(task)
            task.add_done_callback(app_state.background_tasks.discard)
        elif data.data.decode("utf-8") == "STOP_RECORDING":
            # FIXED: Properly track background tasks
            task = asyncio.create_task(handle_stop_recording(room=room))
            app_state.background_tasks.add(task)
            task.add_done_callback(app_state.background_tasks.discard)
            app_state.is_recording = False


    room_info = json.loads(message)
    o_token = api.AccessToken(LiveKitConfig.apiKey, LiveKitConfig.apiSecret)
    # o_token = api.AccessToken()
    service_identity = str(uuid.uuid4())
    token = (
        o_token.with_identity(service_identity)
        .with_grants(api.VideoGrants(room_create=True, room_join=True, can_publish=True, can_subscribe=True, can_publish_data=True, room_record=True, room_admin=True, room=room_info['roomName'], can_update_own_metadata=True,))
        .to_jwt()
    )
    await room.connect(LiveKitConfig.url, token)
    LOGGER.debug("connected to room: " + room.name)

    # Keep the connection alive
    try:
        # Wait indefinitely until the room is disconnected or an error occurs
        await asyncio.Event().wait()
    except Exception as e:
        LOGGER.error(f"Error in main loop: {str(e)}", exc_info=True)
    finally:
        await room.disconnect()

def callback(ch, method, properties, body):
    LOGGER.info(f" [x] Received {body}")

    # Check if the message came from the expected queue by checking the routing key
    # In RabbitMQ, when using direct exchange, the routing key should match the queue name
    if hasattr(method, 'routing_key') and method.routing_key != queue_name:
        LOGGER.info(f"Ignoring message from queue '{method.routing_key}', expected '{queue_name}'")
        # Acknowledge the message to remove it from the queue but don't process it
        return

    thread = threading.Thread(target=start_process, args=(ch, body.decode(), method))
    thread.start()

def start_process(queue_channel, message, queue_method):

    # FIXED: Better event loop management
    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        room = rtc.Room(loop=loop)
        LOGGER.info(f"Received {message}.")

        # Create main task and run until completion or error
        main_task = loop.create_task(main(room, message, queue_channel, queue_method))

        try:
            loop.run_until_complete(main_task)
        except Exception as e:
            LOGGER.error(f"Error in main task: {str(e)}", exc_info=True)
        finally:
            # Close the loop properly
            loop.close()
    except Exception as e:
        LOGGER.error(f"Error in start_process: {str(e)}", exc_info=True)
    finally:
        # Ensure we acknowledge the message even if there's an error
        try:
            queue_channel.basic_ack(delivery_tag=queue_method.delivery_tag)
            close_connection(main_connection, main_channel)
        except Exception as e:
            LOGGER.error(f"Error acknowledging message: {str(e)}", exc_info=True)

main_channel.basic_consume(queue=queue_name, on_message_callback=callback, auto_ack=False)
LOGGER.info(' [*] Waiting for messages. To exit press CTRL+C')
main_channel.start_consuming()